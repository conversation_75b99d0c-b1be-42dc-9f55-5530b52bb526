#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业行业分布分析脚本（按层级详细分析版）
功能：按行业L1、L2、L3层级统计第二产业和第三产业的详细分布
"""

import pandas as pd
import os
import glob
from datetime import datetime
import gc

# 行业分类定义
SECONDARY_INDUSTRIES = {'B', 'C', 'D', 'E'}  # 第二产业
TERTIARY_INDUSTRIES = {'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T'}  # 第三产业

def build_industry_mapping_incrementally(data_dir):
    """
    增量构建企业行业映射表（包含L1、L2、L3层级信息）

    Args:
        data_dir: 数据目录路径

    Returns:
        dict: {企业ID: {l1_code, l1_name, l2_code, l2_name, l3_code, l3_name}}
    """
    print(f"开始构建企业行业映射表: {data_dir}")

    parquet_files = glob.glob(os.path.join(data_dir, "*.parquet"))
    print(f"找到 {len(parquet_files)} 个parquet文件")

    industry_mapping = {}
    total_processed = 0

    # 需要读取的字段
    required_columns = [
        'lc_company_id',
        'industry_l1_code', 'industry_l1_name',
        'industry_l2_code', 'industry_l2_name',
        'industry_l3_code', 'industry_l3_name'
    ]

    for i, file_path in enumerate(parquet_files, 1):
        print(f"处理文件 {i}/{len(parquet_files)}: {os.path.basename(file_path)}")

        try:
            # 读取所需字段
            df = pd.read_parquet(file_path, columns=required_columns)

            # 过滤有效记录（至少有L1行业代码）
            df = df.dropna(subset=['industry_l1_code'])

            # 构建详细的行业信息映射
            for _, row in df.iterrows():
                company_id = row['lc_company_id']
                industry_mapping[company_id] = {
                    'l1_code': row['industry_l1_code'],
                    'l1_name': row.get('industry_l1_name', ''),
                    'l2_code': row.get('industry_l2_code', ''),
                    'l2_name': row.get('industry_l2_name', ''),
                    'l3_code': row.get('industry_l3_code', ''),
                    'l3_name': row.get('industry_l3_name', '')
                }

            total_processed += len(df)

            # 清理内存
            del df

            # 每20个文件进行垃圾回收
            if i % 20 == 0:
                gc.collect()
                print(f"  已处理 {i} 个文件，累计企业: {len(industry_mapping):,}")

        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
            continue

    print(f"映射表构建完成: {len(industry_mapping):,} 个企业")
    return industry_mapping

def analyze_industry_by_levels(analysis_dir, industry_mapping):
    """
    按行业层级分析企业分布（针对2010、2020、2024年）

    Args:
        analysis_dir: 分析结果目录
        industry_mapping: 企业行业映射字典

    Returns:
        dict: 分析结果，包含第二产业和第三产业的L1、L2、L3层级统计
    """
    print(f"开始按层级分析行业分布: {analysis_dir}")

    # 目标年份
    target_years = [2010, 2020, 2024]
    txt_files = glob.glob(os.path.join(analysis_dir, "normal_companies_*.txt"))

    # 存储各年份的企业ID
    year_companies = {}

    # 读取各年份的企业数据
    for file_path in sorted(txt_files):
        filename = os.path.basename(file_path)
        year_str = filename.replace('normal_companies_', '').replace('.txt', '')

        try:
            year = int(year_str)
            if year in target_years:
                print(f"读取 {year} 年企业数据...")

                with open(file_path, 'r', encoding='utf-8') as f:
                    company_ids = [line.strip() for line in f if line.strip()]

                year_companies[year] = company_ids
                print(f"  {year}年: {len(company_ids):,} 家企业")

        except ValueError:
            continue
        except Exception as e:
            print(f"读取文件 {file_path} 时出错: {e}")
            continue

    # 分析结果存储
    results = {
        'secondary': {  # 第二产业
            'l1': {},  # L1层级统计
            'l2': {},  # L2层级统计
            'l3': {}   # L3层级统计
        },
        'tertiary': {   # 第三产业
            'l1': {},  # L1层级统计
            'l2': {},  # L2层级统计
            'l3': {}   # L3层级统计
        }
    }

    # 逐年分析
    for year in target_years:
        if year not in year_companies:
            print(f"警告: 未找到 {year} 年的数据")
            continue

        print(f"分析 {year} 年行业分布...")
        company_ids = year_companies[year]

        # 分别统计第二产业和第三产业
        for industry_type, industry_codes in [('secondary', SECONDARY_INDUSTRIES), ('tertiary', TERTIARY_INDUSTRIES)]:
            print(f"  处理{industry_type}...")

            # 筛选属于当前产业类型的企业
            filtered_companies = []
            for company_id in company_ids:
                industry_info = industry_mapping.get(company_id)
                if industry_info and industry_info['l1_code'] in industry_codes:
                    filtered_companies.append((company_id, industry_info))

            total_count = len(filtered_companies)
            print(f"    {year}年{industry_type}企业总数: {total_count:,}")

            # 按L1、L2、L3层级统计
            for level in ['l1', 'l2', 'l3']:
                level_stats = {}

                for company_id, industry_info in filtered_companies:
                    code_key = f'{level}_code'
                    name_key = f'{level}_name'

                    code = industry_info.get(code_key, '')
                    name = industry_info.get(name_key, '')

                    # 跳过空值
                    if not code or pd.isna(code):
                        continue

                    # 使用行业名称作为键，如果名称为空则使用代码
                    display_name = name if name and not pd.isna(name) else code

                    if display_name not in level_stats:
                        level_stats[display_name] = {}

                    if year not in level_stats[display_name]:
                        level_stats[display_name][year] = 0

                    level_stats[display_name][year] += 1

                # 计算占比并存储结果
                for name, year_data in level_stats.items():
                    if name not in results[industry_type][level]:
                        results[industry_type][level][name] = {}

                    count = year_data.get(year, 0)
                    ratio = (count / total_count * 100) if total_count > 0 else 0

                    results[industry_type][level][name][year] = {
                        'count': count,
                        'ratio': round(ratio, 2)
                    }

    return results

def save_to_excel_by_levels(results, output_file):
    """
    保存按层级分析的结果到Excel文件

    Args:
        results: 分析结果字典
        output_file: 输出文件路径
    """
    print(f"保存结果到: {output_file}")

    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:

        # 处理第二产业和第三产业
        for industry_type, industry_name in [('secondary', '二产'), ('tertiary', '三产')]:
            industry_data = results[industry_type]

            # 处理L1、L2、L3层级
            for level in ['l1', 'l2', 'l3']:
                level_data = industry_data[level]

                if not level_data:
                    continue

                # 构建DataFrame
                rows = []
                for name, year_data in level_data.items():
                    row = {'行业名称': name}

                    # 添加各年份的数据
                    for year in [2010, 2020, 2024]:
                        if year in year_data:
                            count = year_data[year]['count']
                            ratio = year_data[year]['ratio']
                            row[f'{year}年企业数'] = count
                            row[f'{year}年占比(%)'] = ratio
                        else:
                            row[f'{year}年企业数'] = 0
                            row[f'{year}年占比(%)'] = 0.0

                    rows.append(row)

                if rows:
                    df = pd.DataFrame(rows)

                    # 按2024年企业数排序
                    df = df.sort_values(f'2024年企业数', ascending=False)

                    # 保存到对应的sheet
                    sheet_name = f'{industry_name}L{level[-1]}'
                    df.to_excel(writer, sheet_name=sheet_name, index=False)

                    print(f"  已保存 {sheet_name} 数据: {len(df)} 个行业分类")

        # 添加说明sheet
        info_data = [
            ['第二产业', 'B, C, D, E', '采矿业、制造业、电力热力燃气及水生产和供应业、建筑业'],
            ['第三产业', 'F, G, H, I, J, K, L, M, N, O, P, Q, R, S, T', '服务业（批发零售、交通运输、住宿餐饮等）'],
            ['', '', ''],
            ['说明', '', ''],
            ['L1', '', '行业大类（一级分类）'],
            ['L2', '', '行业中类（二级分类）'],
            ['L3', '', '行业小类（三级分类）'],
            ['占比计算', '', '各细分行业占其所属产业（第二产业或第三产业）总量的百分比']
        ]
        info_df = pd.DataFrame(info_data, columns=['分类', '代码范围', '说明'])
        info_df.to_excel(writer, sheet_name='分类说明', index=False)

    print(f"Excel文件保存成功: {output_file}")

def main():
    """主函数"""
    data_dir = "processed_company_data_20250729_182340"
    analysis_dir = "sz_company_status_analysis_20250729_182719"
    timestamp = datetime.now().strftime("%Y%m%d")
    output_file = f"sz_industry_analysis_by_levels_{timestamp}.xlsx"

    print("=" * 60)
    print("企业行业分布分析（按层级详细分析版）")
    print("=" * 60)

    try:
        # 检查目录
        if not os.path.exists(data_dir):
            raise ValueError(f"数据目录不存在: {data_dir}")

        if not os.path.exists(analysis_dir):
            raise ValueError(f"分析结果目录不存在: {analysis_dir}")

        # 1. 构建行业映射表
        print("步骤1: 构建企业行业映射表...")
        industry_mapping = build_industry_mapping_incrementally(data_dir)

        # 2. 按层级分析行业分布
        print("\n步骤2: 按层级分析行业分布...")
        results = analyze_industry_by_levels(analysis_dir, industry_mapping)

        if not results:
            raise ValueError("未找到任何有效的分析数据")

        # 3. 保存结果
        print("\n步骤3: 保存分析结果...")
        save_to_excel_by_levels(results, output_file)

        print("=" * 60)
        print("分析完成！")
        print(f"Excel文件: {output_file}")
        print("=" * 60)

        # 显示概览
        print("\n结果概览:")
        for industry_type, industry_name in [('secondary', '第二产业'), ('tertiary', '第三产业')]:
            print(f"\n{industry_name}:")
            industry_data = results[industry_type]

            for level in ['l1', 'l2', 'l3']:
                level_data = industry_data[level]
                if level_data:
                    print(f"  L{level[-1]}层级: {len(level_data)} 个分类")

                    # 显示前3个最大的分类（按2024年数据）
                    sorted_items = sorted(level_data.items(),
                                        key=lambda x: x[1].get(2024, {}).get('count', 0),
                                        reverse=True)

                    for i, (name, year_data) in enumerate(sorted_items[:3]):
                        if 2024 in year_data:
                            count_2024 = year_data[2024]['count']
                            ratio_2024 = year_data[2024]['ratio']
                            print(f"    {i+1}. {name}: {count_2024:,}家 ({ratio_2024}%)")

    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
