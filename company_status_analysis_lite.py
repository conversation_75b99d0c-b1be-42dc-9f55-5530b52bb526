#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业特定年份成立企业ID提取脚本
功能：提取2010年、2020年、2024年成立的企业ID
"""

import pandas as pd
import os
import glob
from datetime import datetime
from pathlib import Path
import gc

def extract_companies_by_establish_year(data_dir):
    """
    根据成立年份提取特定年份的企业ID

    Args:
        data_dir: 数据目录路径

    Returns:
        dict: {年份: 企业ID集合}
    """
    print(f"开始提取企业数据: {data_dir}")

    parquet_files = glob.glob(os.path.join(data_dir, "*.parquet"))
    print(f"找到 {len(parquet_files)} 个parquet文件")

    # 目标年份
    target_years = [2010, 2020, 2024]

    # 存储结果
    yearly_companies = {year: set() for year in target_years}

    total_processed = 0

    for i, file_path in enumerate(parquet_files, 1):
        print(f"处理文件 {i}/{len(parquet_files)}: {os.path.basename(file_path)}")

        try:
            df = pd.read_parquet(file_path)

            # 过滤掉成立日期为空的记录
            df = df[df['establish_date'].notna()].copy()

            if len(df) == 0:
                continue

            # 提取成立年份
            df['establish_year'] = df['establish_date'].dt.year

            # 按目标年份提取企业ID
            for year in target_years:
                companies = df[df['establish_year'] == year]['lc_company_id'].unique()
                if len(companies) > 0:
                    yearly_companies[year].update(companies)

            total_processed += len(df)

            # 清理内存
            del df
            if i % 20 == 0:
                gc.collect()
                print(f"已处理 {total_processed:,} 条有效记录")

        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
            continue

    print(f"数据提取完成，总处理记录: {total_processed:,}")
    for year in target_years:
        print(f"{year}年成立企业数量: {len(yearly_companies[year]):,}")

    return yearly_companies

def save_results(yearly_companies, output_dir):
    """保存结果"""
    print(f"保存结果到: {output_dir}")
    Path(output_dir).mkdir(exist_ok=True)

    # 保存各年度企业ID列表
    for year in sorted(yearly_companies.keys()):
        company_ids = yearly_companies[year]

        # 保存为文本文件
        txt_file = os.path.join(output_dir, f"established_companies_{year}.txt")
        with open(txt_file, 'w', encoding='utf-8') as f:
            for company_id in sorted(company_ids):
                f.write(f"{company_id}\n")

        print(f"{year}年成立企业: {len(company_ids):,} 个 -> {txt_file}")

    # 保存汇总
    summary_file = os.path.join(output_dir, "yearly_summary.txt")
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("企业成立年份分析汇总\n")
        f.write("=" * 50 + "\n")
        f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        for year in sorted(yearly_companies.keys()):
            count = len(yearly_companies[year])
            f.write(f"{year}年成立企业: {count:,} 个\n")

    print(f"汇总统计: {summary_file}")

def main():
    """主函数"""
    data_dir = "processed_company_data_20250729_182340"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"company_establish_analysis_{timestamp}"

    print("=" * 60)
    print("企业成立年份分析")
    print("=" * 60)

    try:
        if not os.path.exists(data_dir):
            raise ValueError(f"数据目录不存在: {data_dir}")

        # 1. 提取特定年份成立的企业
        yearly_companies = extract_companies_by_establish_year(data_dir)

        # 2. 保存结果
        save_results(yearly_companies, output_dir)

        print("=" * 60)
        print("分析完成！")
        print(f"结果目录: {output_dir}")
        print("=" * 60)

    except Exception as e:
        print(f"错误: {e}")
        raise

if __name__ == "__main__":
    main()
